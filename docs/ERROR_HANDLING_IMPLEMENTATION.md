# 错误处理实现文档

## 概述

本文档描述了为Firespoon Restaurant应用添加的统一错误处理系统。该实现基于现有的Logger系统和Sentry集成，为所有GraphQL操作提供了完整的错误处理和用户反馈机制。

## 主要改进

### 1. Hook层面的错误处理

所有GraphQL mutation hooks现在都包含：

#### **统一的Logger集成**
```javascript
import Logger from '../../core/logger'
const logger = new Logger('HookName')
```

#### **onCompleted和onError回调**
- **onCompleted**: 记录成功操作的详细信息
- **onError**: 记录GraphQL和网络错误

#### **异步函数返回Promise**
- 所有hook函数现在返回Promise
- 支持async/await调用模式
- 允许调用方捕获和处理错误

### 2. 修改的Hooks

#### **useCancelOrder**
- 添加了详细的日志记录
- 记录订单ID、取消原因等上下文信息
- 支持Promise返回

#### **useOrderRing**
- 添加了静音操作的错误处理
- 记录订单ID和操作结果
- 支持Promise返回

#### **useOrderPickedUp**
- 添加了订单完成操作的错误处理
- 记录订单状态变更
- 支持Promise返回

#### **useAcceptOrder**
- 添加了接受订单的错误处理
- 记录准备时间和订单状态
- 支持Promise返回

#### **useRefundOrder**
- 更新了现有的错误处理以保持一致性
- 使用统一的Logger系统
- 改进了日志信息的结构

### 3. 调用层面的错误处理

#### **OrderDetail.js**
- **cancelOrderFunc**: 添加了完整的try-catch错误处理
- **pickUpOrderFunc**: 添加了完整的try-catch错误处理
- 使用Alert.alert显示用户友好的错误消息
- 记录用户操作和错误上下文

#### **OverlayComponent.js**
- **btnPress**: 添加了完整的try-catch错误处理
- 支持打印和非打印模式的错误处理
- 使用Alert.alert显示用户友好的错误消息

### 4. 错误消息策略

#### **用户友好的错误提示**
- 取消订单失败："取消订单失败，请稍后重试"
- 接受订单失败："接受订单失败，请稍后重试"
- 标记完成失败："标记订单完成失败，请稍后重试"
- 静音失败：在hook层面处理，不显示用户提示

#### **详细的日志信息**
- 包含订单ID、操作类型、错误消息
- 区分GraphQL错误和网络错误
- 记录用户操作上下文

### 5. Logger系统集成

#### **日志级别使用**
- **DEBUG**: 操作开始、参数信息
- **INFO**: 操作成功完成
- **ERROR**: 操作失败、错误详情

#### **结构化日志数据**
```javascript
logger.error('Operation failed', {
  orderId: order._id,
  userAction: 'cancel_order',
  error: error.message,
  graphQLErrors: error.graphQLErrors,
  networkError: error.networkError
})
```

### 6. Sentry集成

#### **自动错误上报**
- 生产环境中的错误自动发送到Sentry
- 包含完整的错误堆栈和上下文信息
- 支持错误分类和过滤

#### **环境配置**
- 开发环境：详细日志到console
- 生产环境：关键错误到Sentry

## 测试工具

### **错误处理测试工具** (`src/utils/errorHandlingTest.js`)

提供了完整的测试套件：

#### **testLoggerLevels()**
测试Logger的所有级别输出

#### **testSentryIntegration()**
验证Sentry错误上报功能

#### **testGraphQLErrorHandling()**
模拟GraphQL和网络错误

#### **testOrderOperationErrors()**
测试订单操作的错误场景

#### **runAllErrorHandlingTests()**
运行所有测试

### 使用方法
```javascript
import { runAllErrorHandlingTests } from '../utils/errorHandlingTest'

// 在开发环境中运行测试
if (__DEV__) {
  runAllErrorHandlingTests()
}
```

## 最佳实践

### 1. Hook开发
- 始终使用Logger记录操作
- 提供onCompleted和onError回调
- 返回Promise以支持调用方错误处理

### 2. 调用方错误处理
- 使用async/await和try-catch
- 提供用户友好的错误消息
- 记录用户操作上下文

### 3. 日志记录
- 使用结构化数据
- 包含足够的上下文信息
- 区分不同类型的错误

### 4. 用户体验
- 始终提供错误反馈
- 使用一致的错误消息格式
- 避免技术术语

## 监控和调试

### 开发环境
- 查看console输出获取详细日志
- 使用测试工具验证错误处理

### 生产环境
- 通过Sentry监控错误
- 分析错误模式和频率
- 根据错误数据优化用户体验

## 未来改进

### 1. 错误重试机制
- 为网络错误添加自动重试
- 实现指数退避策略

### 2. 离线错误处理
- 缓存失败的操作
- 网络恢复时重新执行

### 3. 用户错误报告
- 允许用户报告问题
- 收集用户反馈改进错误处理

## 总结

新的错误处理系统提供了：
- 统一的错误处理策略
- 完整的日志记录
- 用户友好的错误反馈
- 生产环境错误监控
- 开发环境调试工具

这确保了应用在遇到错误时能够优雅地处理，并为开发团队提供了充分的调试信息。
