import { useMutation, gql } from '@apollo/client'
import { cancelOrder } from '../../apollo'
import Logger from '../../core/logger'

const logger = new Logger('useCancelOrder')

export default function useCancelOrder() {
  const [mutateCancel, { loading, error }] = useMutation(
    gql`
      ${cancelOrder}
    `,
    {
      onCompleted: (data) => {
        logger.info('Cancel order successful', {
          orderId: data?.cancelOrder?._id,
          orderStatus: data?.cancelOrder?.orderStatus
        })
      },
      onError: (error) => {
        logger.error('Cancel order mutation failed', {
          error: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        })
      }
    }
  )

  const cancelOrderFunc = async (_id, reason) => {
    logger.debug('Starting cancel order operation', { _id, reason })
    try {
      const result = await mutateCancel({ variables: { _id, reason } })
      return result
    } catch (error) {
      logger.error('Cancel order operation failed', {
        _id,
        reason,
        error: error.message
      })
      throw error
    }
  }

  return { loading, error, cancelOrder: cancelOrderFunc }
}
