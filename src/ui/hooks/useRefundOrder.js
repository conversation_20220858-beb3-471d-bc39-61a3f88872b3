import { useMutation, gql } from '@apollo/client'
import { refundOrder, orders } from '../../apollo'
import Logger from '../../core/logger'

const logger = new Logger('useRefundOrder')

const REFUND_ORDER = gql`
  ${refundOrder}
`

const ORDERS = gql`
  ${orders}
`

export default function useRefundOrder() {
  const [mutateRefund, { loading, error, data }] = useMutation(
    REFUND_ORDER,
    {
      refetchQueries: [ORDERS],
      onCompleted: (data) => {
        if (data?.refundOrder?.success) {
          logger.info('Refund order successful', {
            orderId: data.refundOrder.order?._id,
            refundId: data.refundOrder.refund?._id,
            message: data.refundOrder.message
          })
        }
      },
      onError: (error) => {
        logger.error('Refund order mutation failed', {
          error: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        })
      }
    }
  )

  const refundOrderFunc = async (_id, amount, reason, reasonText = '') => {
    logger.debug('Starting refund order operation', {
      _id,
      amount,
      reason,
      reasonText
    })

    try {
      const result = await mutateRefund({
        variables: {
          _id,
          amount: parseFloat(amount),
          reason,
          reasonText
        }
      })
      return result
    } catch (error) {
      logger.error('Refund order operation failed', {
        _id,
        amount,
        reason,
        reasonText,
        error: error.message
      })
      throw error
    }
  }

  return { 
    loading, 
    error, 
    data,
    refundOrder: refundOrderFunc 
  }
}
