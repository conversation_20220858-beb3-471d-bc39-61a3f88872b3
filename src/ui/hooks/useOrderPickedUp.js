import { useMutation, gql } from '@apollo/client'
import { orderPickedUp } from '../../apollo'
import Logger from '../../core/logger'

const logger = new Logger('useOrderPickedUp')

export default function useOrderPickedUp() {
  const [mutatePickedUp, { loading, error }] = useMutation(
    gql`
      ${orderPickedUp}
    `,
    {
      onCompleted: (data) => {
        logger.info('Order picked up successful', {
          orderId: data?.orderPickedUp?._id,
          orderStatus: data?.orderPickedUp?.orderStatus
        })
      },
      onError: (error) => {
        logger.error('Order picked up mutation failed', {
          error: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        })
      }
    }
  )

  const pickedUpOrderFunc = async (_id) => {
    logger.debug('Starting order picked up operation', { _id })
    try {
      const result = await mutatePickedUp({ variables: { _id } })
      return result
    } catch (error) {
      logger.error('Order picked up operation failed', {
        _id,
        error: error.message
      })
      throw error
    }
  }

  return { loading, error, pickedUp: pickedUpOrderFunc }
}
