import { useMutation, gql } from '@apollo/client'
import { muteRingOrder, orders } from '../../apollo'
import Logger from '../../core/logger'

const logger = new Logger('useOrderRing')

const MUTE_RING_ORDER = gql`
  ${muteRingOrder}
`
const ORDERS = gql`
  ${orders}
`

export default function useOrderRing() {
  const [mutate, { loading, error }] = useMutation(MUTE_RING_ORDER, {
    refetchQueries: [ORDERS],
    onCompleted: (data) => {
      logger.info('Mute ring successful', { result: data?.muteRing })
    },
    onError: (error) => {
      logger.error('Mute ring mutation failed', {
        error: error.message,
        graphQLErrors: error.graphQLErrors,
        networkError: error.networkError
      })
    }
  })

  const muteRing = async (id) => {
    logger.debug('Starting mute ring operation', { orderId: id })
    try {
      const result = await mutate({ variables: { orderId: id } })
      return result
    } catch (error) {
      logger.error('Mute ring operation failed', {
        orderId: id,
        error: error.message
      })
      throw error
    }
  }

  return { loading, error, muteRing }
}
