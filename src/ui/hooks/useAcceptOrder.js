import { useMutation, gql } from '@apollo/client'
import { acceptOrder } from '../../apollo'
import Logger from '../../core/logger'

const logger = new Logger('useAcceptOrder')

export default function useAcceptOrder() {
  const [mutateAccept, { loading, error }] = useMutation(
    gql`
      ${acceptOrder}
    `,
    {
      onCompleted: (data) => {
        logger.info('Accept order successful', {
          orderId: data?.acceptOrder?._id,
          orderStatus: data?.acceptOrder?.orderStatus,
          preparationTime: data?.acceptOrder?.preparationTime
        })
      },
      onError: (error) => {
        logger.error('Accept order mutation failed', {
          error: error.message,
          graphQLErrors: error.graphQLErrors,
          networkError: error.networkError
        })
      }
    }
  )

  const acceptOrderFunc = async (_id, time) => {
    logger.debug('Starting accept order operation', { _id, time })
    try {
      const result = await mutateAccept({ variables: { _id, time } })
      return result
    } catch (error) {
      logger.error('Accept order operation failed', {
        _id,
        time,
        error: error.message
      })
      throw error
    }
  }

  return { loading, error, acceptOrder: acceptOrderFunc }
}
