import React, { useState } from 'react'
import { View, Pressable, TouchableOpacity, Alert } from 'react-native'
import { Spinner, TextDefault } from '..'
import styles from './styles'
import { colors, TIMES } from '../../utilities'
import { Overlay } from 'react-native-elements'
import { useAcceptOrder, usePrintOrder, useOrderRing } from '../../ui/hooks'
import Logger from '../../core/logger'

const logger = new Logger('OverlayComponent')

export default function OverlayComponent(props) {
  const { visible, toggle, order, print, navigation } = props
  const [selectedTime, setSelectedTime] = useState(TIMES[0])
  const { acceptOrder, loading } = useAcceptOrder()
  const { muteRing } = useOrderRing()
  const { printOrder } = usePrintOrder()

  const btnPress = async () => {
    try {
      logger.debug('User initiated accept order', {
        orderId: order._id,
        orderIdShort: order.orderId,
        selectedTime,
        withPrint: print
      })

      await acceptOrder(order._id, selectedTime.toString())
      await muteRing(order.orderId)

      if (print) {
        await printOrder(order._id)
        logger.info('Order accepted and printed successfully', {
          orderId: order._id
        })
      } else {
        logger.info('Order accepted successfully', {
          orderId: order._id
        })
      }

      toggle()
      navigation.goBack()
    } catch (error) {
      logger.error('Accept order operation failed', {
        orderId: order._id,
        userAction: 'accept_order',
        withPrint: print,
        error: error.message
      })

      Alert.alert(
        "错误",
        "接受订单失败，请稍后重试",
        [{ text: "确定", style: "default" }]
      )
    }
  }
  return (
    <Overlay
      isVisible={visible}
      onBackdropPress={toggle}
      overlayStyle={styles.container}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TextDefault H1 bolder>
            Set Time
          </TextDefault>
          <TextDefault bold>For Preparation</TextDefault>
        </View>
        <View style={styles.time}>
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap'
            }}>
            {TIMES.map((time, index) => (
              <Pressable
                key={index}
                onPress={() => setSelectedTime(time)}
                style={[
                  styles.timeBtn,
                  {
                    backgroundColor:
                      selectedTime === time ? 'black' : colors.white
                  }
                ]}>
                <TextDefault
                  small
                  style={{
                    color: selectedTime === time ? colors.darkgreen : 'black'
                  }}>
                  {time + ' mins '}
                </TextDefault>
              </Pressable>
            ))}
          </View>
        </View>
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.btn}
          onPress={btnPress}>
          <TextDefault bold style={{ color: colors.darkgreen }}>
            Set & accept
          </TextDefault>
        </TouchableOpacity>
      </View>
    </Overlay>
  )
}
