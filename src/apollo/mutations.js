export const login = `mutation RestaurantLogin($username:String!,$password:String!){
    restaurantLogin(username:$username,password:$password){
        token
        restaurantId
    }
}`

export const acceptOrder = `mutation AcceptOrder($_id:String!, $time:String){
    acceptOrder(_id:$_id, time:$time){
        _id
      orderStatus
      preparationTime
    }
}`

export const cancelOrder = `mutation CancelOrder($_id:String!,$reason:RefundReason!){
    cancelOrder(_id:$_id,reason:$reason){
        _id
      orderStatus
    }
}`
export const orderPickedUp = `mutation OrderPickedUp($_id:String!){
  orderPickedUp(_id:$_id){
        _id
      orderStatus
    }
}`

export const saveToken = `mutation saveRestaurantToken($token:String, $isEnabled:Boolean){
  saveRestaurantToken(token:$token, isEnabled: $isEnabled ){
    _id
    notificationToken
    enableNotification
  }
}`

export const toggleAvailability = `mutation ToggleAvailability{
  toggleAvailability{
    _id
    isAvailable
  }
}`
export const muteRingOrder = `mutation muteRing($orderId:String){
  muteRing(orderId:$orderId)
}`

export const refundOrder = `mutation RefundOrder($_id: String!, $amount: Float!, $reason: RefundReason!, $reasonText: String){
  refundOrder(_id: $_id, amount: $amount, reason: $reason, reasonText: $reasonText){
    success
    refund {
      _id
      refundId
      orderId
      refundType
      requestAmount
      finalRefundAmount
      reason
      reasonText
      feeBearer
      transactionFee
      status
      stripeRefundId
      createdAt
      processedAt
      completedAt
      errorMessage
    }
    order {
      _id
      orderId
      orderStatus
      refunds {
        _id
        refundId
        orderId
        refundType
        requestAmount
        finalRefundAmount
        reason
        reasonText
        feeBearer
        transactionFee
        status
        stripeRefundId
        createdAt
        processedAt
        completedAt
        errorMessage
      }
      totalRefunded
      refundStatus
    }
    message
  }
}`
