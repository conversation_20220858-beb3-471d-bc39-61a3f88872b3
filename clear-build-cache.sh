#!/bin/bash

# 本地Build缓存清理脚本
# 用于清理React Native和Expo的所有缓存

echo "🧹 开始清理本地Build缓存..."

# 1. 停止所有相关进程
echo "📱 停止Metro bundler和相关进程..."
pkill -f "metro" || true
pkill -f "expo" || true
pkill -f "react-native" || true

# 2. 清理Metro缓存
echo "🚇 清理Metro缓存..."
rm -rf /tmp/metro-*
rm -rf /tmp/react-*
rm -rf /tmp/haste-map-*

# 3. 清理node_modules缓存
echo "📦 清理node_modules缓存..."
rm -rf node_modules/.cache
rm -rf .expo

# 4. 清理系统缓存
echo "💾 清理系统缓存..."
rm -rf ~/.cache/yarn || true
rm -rf ~/.npm/_cacache || true
rm -rf ~/.cache/expo || true

# 5. 清理Watchman缓存（如果安装了）
if command -v watchman &> /dev/null; then
    echo "👁️ 清理Watchman缓存..."
    watchman watch-del-all
else
    echo "ℹ️ Watchman未安装，跳过Watchman缓存清理"
fi

# 6. 清理Gradle缓存（Android）
if [ -d "android" ]; then
    echo "🤖 清理Android/Gradle缓存..."
    cd android
    ./gradlew clean || true
    rm -rf .gradle
    rm -rf build
    rm -rf app/build
    cd ..
fi

# 7. 清理iOS缓存
if [ -d "ios" ]; then
    echo "🍎 清理iOS缓存..."
    cd ios
    rm -rf build
    rm -rf Pods
    rm -rf ~/Library/Developer/Xcode/DerivedData/* || true
    cd ..
fi

# 8. 重新安装依赖
echo "📥 重新安装依赖..."
npm install

# 9. 清理Expo缓存
echo "🔄 清理Expo缓存..."
npx expo install --fix

echo "✅ 本地Build缓存清理完成！"
echo ""
echo "🚀 现在可以重新启动应用："
echo "   npx expo start --clear"
echo "   或"
echo "   npx react-native start --reset-cache"
